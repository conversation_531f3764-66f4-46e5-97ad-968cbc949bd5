<template>
  <!-- 价量分析页面 -->
  <div class="cw-jlfx-page">
    <a-card :bordered="false">
      <template #title>
        金属利润影响分析 (瀑布图)
      </template>
      <template #extra>
        <a-radio-group v-model:value="chartType" button-style="solid" size="middle">
          <a-radio-button value="price">价格影响</a-radio-button>
          <a-radio-button value="volume">产量影响</a-radio-button>
        </a-radio-group>
      </template>
      <!-- 查询条件 -->
      <a-space style="margin-bottom: 12px;" align="center">
        <a-radio-group v-model:value="dimension">
          <a-radio-button value="month">月</a-radio-button>
          <a-radio-button value="year">年</a-radio-button>
        </a-radio-group>
        <a-date-picker
          v-model:value="barDate"
          :picker="pickerType"
          :allowClear="false"
        />
      </a-space>

      <!-- 影响概览卡片 -->
      <div class="impact-summary">
        <!-- 价格影响列表 -->
        <div class="impact-item price" :class="{ active: chartType === 'price' }">
          <div class="label">价格影响 (万元)</div>
          <div class="impact-list">
            <div class="impact-row" v-for="p in priceImpactList" :key="p.metal">
              <span class="metal">{{ p.metal }}</span>
              <span :class="p.value >= 0 ? 'pos' : 'neg'">{{ formatNumber(p.value) }}</span>
            </div>
          </div>
          <a-divider style="margin:6px 0" />
          <div class="total">合计：{{ formatNumber(totalPriceImpact) }}</div>
        </div>
        <!-- 产量影响列表 -->
        <div class="impact-item volume" :class="{ active: chartType === 'volume' }">
          <div class="label">产量影响 (万元)</div>
          <div class="impact-list">
            <div class="impact-row" v-for="v in volumeImpactList" :key="v.metal">
              <span class="metal">{{ v.metal }}</span>
              <span :class="v.value >= 0 ? 'pos' : 'neg'">{{ formatNumber(v.value) }}</span>
            </div>
          </div>
          <a-divider style="margin:6px 0" />
          <div class="total">合计：{{ formatNumber(totalVolumeImpact) }}</div>
        </div>
      </div>

      <div ref="barRef" class="bar-chart"></div>
    </a-card>
  </div>
</template>

<script lang="ts" setup name="cw-price-volume-statistics">
  // 价量分析脚本
  import { ref, onMounted, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { message } from 'ant-design-vue';

  // 接口
  import { metalProfitBar } from '/@/api/cw/statistics';
  // 工具
  import { useECharts } from '/@/hooks/web/useECharts';
  import { formatNumber } from '/@/utils/showUtils';

  /** 查询条件 */
  const dimension = ref<'month' | 'year'>('month');
  const barDate = ref(dayjs());
  const pickerType = computed(() => dimension.value);

  /** 图表类型切换 */
  const chartType = ref<'price' | 'volume'>('price');

  /** 图表实例 */
  const barRef = ref<HTMLDivElement | null>(null);
  // @ts-ignore
  const { setOptions: setBarOptions } = useECharts(barRef as any);

  /** 数据源 */
  const barList = ref<any[]>([]);

  /** 影响汇总 */
  const totalPriceImpact = computed(() => {
    return barList.value.reduce((sum, item) => sum + Number(item.priceImpact ?? 0), 0);
  });
  const totalVolumeImpact = computed(() => {
    return barList.value.reduce((sum, item) => sum + Number(item.volumeImpact ?? 0), 0);
  });

  /** 分金属影响列表 */
  const priceImpactList = computed(() => {
    return barList.value.map((item: any) => ({ metal: item.metal, value: Number(item.priceImpact ?? 0) }));
  });
  const volumeImpactList = computed(() => {
    return barList.value.map((item: any) => ({ metal: item.metal, value: Number(item.volumeImpact ?? 0) }));
  });

  /** 监听变化自动刷新 */
  watch([dimension, barDate], () => fetchBarData());
  watch(chartType, () => updateWaterfallChart());

  onMounted(() => {
    fetchBarData();
  });

  /** 获取柱状图数据 */
  async function fetchBarData() {
    try {
      const dateStr = barDate.value.format('YYYY-MM-DD');
      barList.value = await metalProfitBar({ date: dateStr, dimension: dimension.value });
      updateWaterfallChart();
    } catch (e) {
      console.error(e);
      message.error('金属利润数据获取失败');
    }
  }

  /**
   * 生成瀑布图（自定义系列，跨越0轴颜色保持一致）
   * 基于 waterfall.html 的渲染逻辑
   */
  function generateWaterfallConfig(names: string[], values: number[], type: 'price' | 'volume') {
    const posColor = '#ff7875';
    const negColor = '#95de64';
    const totalColor = '#1890ff';

    let cumulative = 0;
    const items = names.map((name, idx) => {
      const change = Number(values[idx] ?? 0);
      const prev = cumulative;
      const next = prev + change;
      cumulative = next;
      return { name: String(name), prev, next, change };
    });

    // 追加期末总计
    const finalTotal = cumulative;
    items.push({ name: '期末', prev: 0, next: finalTotal, change: finalTotal });

    const seriesData = items.map((d) => [d.name, d.prev, d.next, d.change, d.name === '期末' ? 1 : 0]);

    const title = type === 'price' ? '价格影响瀑布图' : '产量影响瀑布图';
    const titleColor = type === 'price' ? '#1890ff' : '#52c41a';

    return {
      title: { text: title, left: 'center', textStyle: { fontSize: 18, fontWeight: 'bold', color: titleColor } },
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          const d = params?.data;
          if (!d) return '';
          const name = d[0];
          const prev = d[1];
          const next = d[2];
          const change = d[3];
          const isTotal = d[4] === 1;
          if (isTotal) {
            return `${name}<br/>总计：${formatNumber(next)}`;
          }
          const sign = change > 0 ? '+' : '';
          return `${name}<br/>起始: ${formatNumber(prev)}<br/>变动: ${sign}${formatNumber(change)}<br/>结束: ${formatNumber(next)}`;
        },
      },
      grid: { left: 80, right: 40, bottom: 60, top: 60, containLabel: true },
      xAxis: {
        type: 'category',
        data: items.map((d) => d.name),
        axisLabel: {
          rotate: 45,
          formatter: (val: string) => String(val).replace(/[（(].*?[)）]/g, '').trim()
        }
      },
      yAxis: { type: 'value', axisLabel: { formatter: (v: any) => formatNumber(v) }, name: '', nameLocation: 'middle', nameGap: 40 },
      series: [
        {
          type: 'custom',
          renderItem: function (_: any, api: any) {
            const name = api.value(0);
            const prev = api.value(1);
            const next = api.value(2);
            const change = api.value(3);
            const isTotal = api.value(4) === 1;

            const pPrev = api.coord([name, prev]);
            const pNext = api.coord([name, next]);
            const pZero = api.coord([name, 0]);

            const band = Math.max(api.size([1, 0])[0], 20);
            const barW = Math.max(band * 0.6, 8);
            const x = pPrev[0] - barW / 2;

            const children: any[] = [];
            const minH = 2;
            const mainColor = isTotal ? totalColor : (change >= 0 ? posColor : negColor);

            if ((prev >= 0 && next >= 0) || (prev <= 0 && next <= 0)) {
              const topY = Math.min(pPrev[1], pNext[1]);
              const h = Math.max(Math.abs(pNext[1] - pPrev[1]), minH);
              children.push({
                type: 'rect',
                shape: { x: x, y: topY, width: barW, height: h, r: 3 },
                style: api.style({ fill: mainColor })
              });
            } else {
              const topY = Math.min(pPrev[1], pZero[1]);
              const hTop = Math.max(Math.abs(pZero[1] - pPrev[1]), minH);
              children.push({
                type: 'rect',
                shape: { x: x, y: topY, width: barW, height: hTop, r: 3 },
                style: api.style({ fill: mainColor })
              });
              const topY2 = Math.min(pZero[1], pNext[1]);
              const hBot = Math.max(Math.abs(pNext[1] - pZero[1]), minH);
              children.push({
                type: 'rect',
                shape: { x: x, y: topY2, width: barW, height: hBot, r: 3 },
                style: api.style({ fill: mainColor })
              });
            }

            const labelY = Math.min(pPrev[1], pNext[1]) - 6;
            const labelText = isTotal ? formatNumber(next) : ((change > 0 ? '+' : '') + formatNumber(change));
            children.push({
              type: 'text',
              style: {
                text: labelText,
                x: x + barW / 2,
                y: labelY,
                textAlign: 'center',
                textVerticalAlign: 'bottom',
                fill: '#000',
                font: '12px sans-serif'
              }
            });

            return { type: 'group', children } as any;
          },
          data: seriesData
        }
      ]
    } as any;
  }

  /** 更新瀑布图 */
  function updateWaterfallChart() {
    if (!barList.value?.length) {
      // 可选：清空或显示无数据状态
      setBarOptions({ series: [] });
      return;
    }
    const metals = barList.value.map((d) => d.metal || '--');
    const impacts = barList.value.map((d) => Number(chartType.value === 'price' ? d.priceImpact : d.volumeImpact ?? 0));
    const waterfallOptions = generateWaterfallConfig(metals, impacts, chartType.value);
    setBarOptions(waterfallOptions);
  }
</script>

<style scoped lang="less">
.cw-jlfx-page {
  .ant-card {
    margin-bottom: 16px;
    .bar-chart {
      width: 100%;
      height: calc(100vh - 200px); // 视口自适应高度
    }

    /* ==== Impact Cards Modern Style ==== */
    .impact-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      gap: 16px;
      margin: 12px 0;
    }

    .impact-item {
      background: #ffffff;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      padding: 16px 20px;
      display: flex;
      flex-direction: column;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &.active {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        transform: translateY(-2px);
      }

      &.price.active {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
      }

      &.volume.active {
        border-color: #52c41a;
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.15);
      }
    }

    .impact-item .label {
      font-size: 16px;
      font-weight: 600;
      color: #595959;
      margin-bottom: 8px;
    }

    .impact-list {
      flex: 1;
      border-top: 1px dashed #f0f0f0;
      padding-top: 8px;
      margin-top: 8px;
    }

    .impact-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 4px; /* 增加左右间隙 */
      font-size: 14px;
    }

    .impact-row:not(:last-child) {
      border-bottom: 1px dashed #f0f0f0;
    }

    .impact-row .metal {
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }

    .impact-row .metal::before {
      content: "";
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #1890ff;
    }

    .impact-row .pos {
      color: #f5222d;
    }
    .impact-row .neg {
      color: #52c41a;
    }

    .total {
      margin-top: 10px;
      font-weight: 600;
      text-align: right;
      font-size: 18px; /* 更突出 */
    }
  }
}
</style>
