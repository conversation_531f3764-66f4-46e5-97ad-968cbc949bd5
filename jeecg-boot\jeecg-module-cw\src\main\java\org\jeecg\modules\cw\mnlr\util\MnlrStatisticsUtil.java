package org.jeecg.modules.cw.mnlr.util;

import cn.hutool.core.date.DateUtil;
import org.jeecg.modules.cw.base.service.ICwKBaseService;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDayRow;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrStatisticsDay;
import org.jeecg.modules.cw.mnlr.result.CwMnlrDayQueryResult;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模拟利润统计工具类
 * 提供计算模拟利润相关数据的方法
 *
 * <AUTHOR>
 */
public class MnlrStatisticsUtil {

    

    /**
     * 计算模拟利润统计数据
     *
     * @param result 模拟利润查询结果
     * @param recordDate 记录日期，如果为null则使用当天日期
     * @return 模拟利润统计数据对象
     */
    public static CwMnlrStatisticsDay calculateStatistics(CwMnlrDayQueryResult result, Date recordDate) {
        return calculateStatistics(result, recordDate, null);
    }

    /**
     * 计算模拟利润统计数据（使用 kBaseService 获取成本）
     *
     * @param result 模拟利润查询结果
     * @param recordDate 记录日期，如果为null则使用当天日期
     * @param kBaseService 基础数据服务，用于获取总成本，如果为null则使用传统计算方式
     * @return 模拟利润统计数据对象
     */
    public static CwMnlrStatisticsDay calculateStatistics(CwMnlrDayQueryResult result, Date recordDate, ICwKBaseService kBaseService) {
        if (result == null || result.getRows() == null || result.getRows().isEmpty()) {
            return null;
        }

        // 如果未指定日期，则使用当天日期
        Date today = recordDate != null ? recordDate : DateUtil.beginOfDay(new Date());

        // 获取各类型数据
        List<CwMnlrDayRow> rows = result.getRows();
        Map<String, CwMnlrDayRow> rowMap = rows.stream().collect(Collectors.toMap(CwMnlrDayRow::getType, row -> row));

        // 获取各类型数据
        CwMnlrDayRow tData = rowMap.get("t");  // 含铜
        CwMnlrDayRow jData = rowMap.get("j");  // 含金
        CwMnlrDayRow yData = rowMap.get("y");  // 含银
        CwMnlrDayRow lData = rowMap.get("l");  // 含硫
        CwMnlrDayRow ljkData = rowMap.get("ljk");  // 硫精矿
        CwMnlrDayRow mjkData = rowMap.get("mjk");  // 钼精矿

        // 计算销售收入总和
        BigDecimal slSum = calculateSalesSum(tData, jData, yData, lData, ljkData, mjkData);

        // 计算成本总和 - 使用新的逻辑
        BigDecimal cbSum;
        if (kBaseService != null) {
            // 使用 kBaseService.getTotalDrs(day) 获取成本数据
            cbSum = kBaseService.getTotalDrs(today);
        } else {
            throw new IllegalArgumentException("kBaseService 不能为 null");
        }

        // 获取其他费用和分公司及多经单位利润
        BigDecimal qtfy = getSafeValue(result.getQtfy());
        BigDecimal frdw = getSafeValue(result.getFrdw());
        BigDecimal jh = getSafeValue(result.getJh());   

        // 计算模拟利润（不含分公司及多经单位利润）
        BigDecimal baseSimProfit = slSum.subtract(cbSum).subtract(qtfy);
        // 合计模拟利润 = 模拟利润 + 分公司及多经单位利润
        BigDecimal totalSimProfit = baseSimProfit.add(frdw);

        // 计算公司进度计划（月计划/12）
        BigDecimal gsjh = jh.divide(new BigDecimal("12"), 2, java.math.RoundingMode.HALF_UP);

        // 计算与进度计划比（以“合计模拟利润”口径：合计模拟利润 - 计划）
        BigDecimal jhb = totalSimProfit.subtract(gsjh);

        // 创建统计数据对象
        CwMnlrStatisticsDay statisticsDay = new CwMnlrStatisticsDay();
        statisticsDay.setSl(slSum.toString());
        statisticsDay.setCb(cbSum);
        statisticsDay.setDjlr(frdw);
        statisticsDay.setQt(qtfy);
        statisticsDay.setMnlr(totalSimProfit);
        statisticsDay.setBaseMnlr(baseSimProfit);
        statisticsDay.setGsjh(gsjh);
        statisticsDay.setJhb(jhb);
        statisticsDay.setRecordTime(today);
        statisticsDay.setUnit("万元");

        return statisticsDay;
    }
    
    /**
     * 计算销售收入总和
     */
    private static BigDecimal calculateSalesSum(CwMnlrDayRow tData, CwMnlrDayRow jData, CwMnlrDayRow yData, CwMnlrDayRow lData, CwMnlrDayRow ljkData, CwMnlrDayRow mjkData) {
        BigDecimal tValue = calculateSingleSales(tData);
        BigDecimal jValue = calculateSingleSales(jData);
        BigDecimal yValue = calculateSingleSales(yData);
        BigDecimal lValue = calculateSingleSales(lData);
        BigDecimal ljkValue = calculateSingleSales(ljkData);
        BigDecimal mjkValue = calculateSingleSales(mjkData);
        
        return tValue.add(jValue).add(yValue).add(lValue).add(ljkValue).add(mjkValue);
    }
    
    /**
     * 计算单项销售收入：金属实际价格 * 公司实际结算系数 * 实际销量 / 1.13 / 10000
     */
    private static BigDecimal calculateSingleSales(CwMnlrDayRow data) {
        if (data == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal jg = getSafeValue(data.getJg());
        BigDecimal xs = getSafeValue(data.getXs());
        BigDecimal xl = getSafeValue(data.getXl());
        
        return jg.multiply(xs).multiply(xl)
                .divide(new BigDecimal("1.13"), 10, java.math.RoundingMode.HALF_UP)
                .divide(new BigDecimal("10000"), 10, java.math.RoundingMode.HALF_UP);
    }
    
    /**
     * 计算成本总和
     */
    private static BigDecimal calculateCostSum(CwMnlrDayRow tData, CwMnlrDayRow jData, CwMnlrDayRow yData, CwMnlrDayRow lData, CwMnlrDayRow ljkData, CwMnlrDayRow mjkData) {
        BigDecimal tCb = getSafeValue(tData != null ? tData.getCb() : null);
        BigDecimal jCb = getSafeValue(jData != null ? jData.getCb() : null);
        BigDecimal yCb = getSafeValue(yData != null ? yData.getCb() : null);
        BigDecimal lCb = getSafeValue(lData != null ? lData.getCb() : null);
        BigDecimal ljkCb = getSafeValue(ljkData != null ? ljkData.getCb() : null);
        BigDecimal mjkCb = getSafeValue(mjkData != null ? mjkData.getCb() : null);
        
        return tCb.add(jCb).add(yCb).add(lCb).add(ljkCb).add(mjkCb);
    }
    
    /**
     * 安全获取BigDecimal值，避免空指针
     */
    private static BigDecimal getSafeValue(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }
}