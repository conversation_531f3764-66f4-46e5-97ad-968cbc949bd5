package org.jeecg.modules.cw.ckc.service;

import org.jeecg.modules.cw.ckc.param.CwCkcZhcbSumbitParam;
import org.jeecg.modules.cw.ckc.result.CwCkcZhcbListResult;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface ICwCkcZhcbService {

    CwCkcZhcbListResult query(Date queryDate);

    void submit(CwCkcZhcbSumbitParam param);

    BigDecimal sumDrs(Date queryDate);

    List<CwKrbRow> sumByMonth(Date queryDate);

    /**
     * 统计指定月份的预算合计金额（yys 字段之和）
     */
    BigDecimal sumBudgetMonth(Date monthDate);

    CwCkcZhcbListResult autoFill(Date queryDate);

    /**
     * 根据日期重新计算drs
     * @param date
     */
    void recalculateDrsByDate(Date date);

    /**
     * 重新计算所有的drs
     */
    void recalculateAllDrs();

    /**
     * 重新计算当日的drs
     */
    void recalculateTodayDrs();

    /**
     * 重新计算指定日期范围内的drs
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    void recalculateDrs(Date startDate, Date endDate);
}
